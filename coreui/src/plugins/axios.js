import axios from 'axios';
import { storePromise } from '../store';

/**
 * Logger utility for consistent logging
 */
class Logger {
  constructor(prefix = 'ProgressTracker') {
    this.prefix = prefix;
  }

  debug(message, data = null) {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${this.prefix}] ${message}`, data || '');
    }
  }

  warn(message, data = null) {
    console.warn(`[${this.prefix}] ${message}`, data || '');
  }

  error(message, error = null) {
    console.error(`[${this.prefix}] ${message}`, error || '');
  }
}

/**
 * Throttle utility to limit function calls
 */
class Throttler {
  constructor(func, delay = 100) {
    this.func = func;
    this.delay = delay;
    this.lastCall = 0;
    this.timeoutId = null;
  }

  call(...args) {
    const now = Date.now();
    const timeSinceLastCall = now - this.lastCall;

    if (timeSinceLastCall >= this.delay) {
      this.lastCall = now;
      this.func(...args);
    } else {
      // Schedule the call for later
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
      }
      this.timeoutId = setTimeout(() => {
        this.lastCall = Date.now();
        this.func(...args);
      }, this.delay - timeSinceLastCall);
    }
  }

  cancel() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

/**
 * Data formatter utility
 */
class ProgressFormatter {
  static formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  static formatTime(seconds) {
    if (!seconds || seconds === Infinity || isNaN(seconds)) return 'Calculating...';

    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.round(seconds % 60);
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }

  static formatSpeed(bytesPerSecond) {
    if (!bytesPerSecond || bytesPerSecond <= 0) return '0 B/s';
    return `${this.formatBytes(bytesPerSecond)}/s`;
  }
}

/**
 * Request type detector
 */
class RequestTypeDetector {
  static detect(config) {
    if (!config) return 'request';

    const method = config.method?.toLowerCase();
    const url = config.url?.toLowerCase() || '';

    if (method === 'post' && config.data instanceof FormData) {
      return 'upload';
    }
    if (url.includes('/export') || url.includes('/download')) {
      return 'download';
    }
    if (url.includes('/import')) {
      return 'import';
    }
    return 'request';
  }
}

/**
 * Progress calculation utility
 */
class ProgressCalculator {
  constructor() {
    this.speedSmoothingFactor = 0.7;
    this.speedSensitivity = 0.3;
  }

  calculateProgress(loaded, total) {
    if (!total || total <= 0) return 0;
    const progress = Math.min((loaded / total) * 100, 100);
    return Math.round(progress * 100) / 100; // Round to 2 decimal places
  }

  calculateSpeed(currentBytes, previousBytes, timeDelta) {
    if (!timeDelta || timeDelta <= 0) return 0;

    const bytesTransferred = Math.max(0, currentBytes - (previousBytes || 0));
    return (bytesTransferred / timeDelta) * 1000; // bytes per second
  }

  smoothSpeed(currentSpeed, previousSpeed) {
    if (!previousSpeed) return currentSpeed;
    return (previousSpeed * this.speedSmoothingFactor) + (currentSpeed * this.speedSensitivity);
  }

  calculateETA(remainingBytes, speed) {
    if (!speed || speed <= 0 || !remainingBytes || remainingBytes <= 0) {
      return 0;
    }
    return remainingBytes / speed;
  }
}

/**
 * Core progress tracking functionality
 */
class ProgressTracker {
  constructor(options = {}) {
    this.options = {
      maxActiveRequests: 100,
      cleanupInterval: 30000, // 30 seconds
      requestTimeout: 300000, // 5 minutes
      throttleDelay: 100, // 100ms
      ...options
    };

    this.activeRequests = new Map();
    this.requestCounter = 0;
    this.logger = new Logger('ProgressTracker');
    this.calculator = new ProgressCalculator();
    this.cleanupTimer = null;
    this.abortControllers = new Map();

    this.startCleanupTimer();
  }

  /**
   * Start tracking a request with proper error handling
   */
  startTracking(config) {
    try {
      // Enforce request limits
      if (this.activeRequests.size >= this.options.maxActiveRequests) {
        this.logger.warn('Max active requests reached, cleaning up oldest');
        this.cleanupOldestRequests();
      }

      const requestId = this.generateRequestId();
      const startTime = Date.now();

      // Create abort controller for cancellation support
      const abortController = new AbortController();
      this.abortControllers.set(requestId, abortController);

      const trackingData = {
        id: requestId,
        startTime,
        lastUpdateTime: startTime,
        totalBytes: 0,
        loadedBytes: 0,
        progress: 0,
        speed: 0,
        estimatedTimeRemaining: 0,
        elapsedTime: 0,
        type: RequestTypeDetector.detect(config),
        url: config.url,
        method: config.method?.toUpperCase() || 'GET',
        status: 'active',
        abortController
      };

      this.activeRequests.set(requestId, trackingData);
      config._progressTrackerId = requestId;

      // Add abort signal to config
      config.signal = abortController.signal;

      this.logger.debug(`Started tracking request ${requestId}`, trackingData);
      return trackingData;
    } catch (error) {
      this.logger.error('Failed to start tracking request', error);
      return null;
    }
  }

  /**
   * Update progress with comprehensive error handling
   */
  updateProgress(requestId, progressEvent) {
    try {
      const tracking = this.activeRequests.get(requestId);
      if (!tracking) {
        this.logger.warn(`Request ${requestId} not found for progress update`);
        return null;
      }

      // Validate progress event
      if (!progressEvent || typeof progressEvent !== 'object') {
        this.logger.warn('Invalid progress event received', progressEvent);
        return null;
      }

      const now = Date.now();
      const elapsedTime = now - tracking.startTime;
      const timeSinceLastUpdate = now - tracking.lastUpdateTime;

      // Update basic progress data with validation
      const loadedBytes = Math.max(0, progressEvent.loaded || 0);
      const totalBytes = Math.max(0, progressEvent.total || 0);

      // Calculate progress
      const progress = this.calculator.calculateProgress(loadedBytes, totalBytes);

      // Calculate speed
      const currentSpeed = this.calculator.calculateSpeed(
        loadedBytes,
        tracking.lastLoadedBytes,
        timeSinceLastUpdate
      );

      const smoothedSpeed = this.calculator.smoothSpeed(currentSpeed, tracking.speed);

      // Calculate ETA
      const remainingBytes = Math.max(0, totalBytes - loadedBytes);
      const estimatedTimeRemaining = this.calculator.calculateETA(remainingBytes, smoothedSpeed);

      // Update tracking data
      Object.assign(tracking, {
        loadedBytes,
        totalBytes,
        progress,
        speed: smoothedSpeed,
        estimatedTimeRemaining,
        elapsedTime,
        lastUpdateTime: now,
        lastLoadedBytes: loadedBytes,
        status: progress >= 100 ? 'completing' : 'active'
      });

      this.logger.debug(`Updated progress for request ${requestId}`, {
        progress: `${progress}%`,
        speed: ProgressFormatter.formatSpeed(smoothedSpeed),
        eta: ProgressFormatter.formatTime(estimatedTimeRemaining)
      });

      return { ...tracking };
    } catch (error) {
      this.logger.error(`Failed to update progress for request ${requestId}`, error);
      return null;
    }
  }

  /**
   * Finish tracking with cleanup
   */
  finishTracking(requestId, status = 'completed') {
    try {
      const tracking = this.activeRequests.get(requestId);
      if (!tracking) {
        this.logger.warn(`Request ${requestId} not found for finish tracking`);
        return null;
      }

      // Update final state
      tracking.elapsedTime = Date.now() - tracking.startTime;
      tracking.status = status;
      tracking.progress = status === 'completed' ? 100 : tracking.progress;

      // Cleanup
      this.cleanupRequest(requestId);

      this.logger.debug(`Finished tracking request ${requestId}`, {
        status,
        elapsedTime: ProgressFormatter.formatTime(tracking.elapsedTime / 1000)
      });

      return { ...tracking };
    } catch (error) {
      this.logger.error(`Failed to finish tracking request ${requestId}`, error);
      return null;
    }
  }

  /**
   * Cancel a specific request
   */
  cancelRequest(requestId) {
    try {
      const abortController = this.abortControllers.get(requestId);
      if (abortController) {
        abortController.abort();
        this.logger.debug(`Cancelled request ${requestId}`);
      }

      this.finishTracking(requestId, 'cancelled');
    } catch (error) {
      this.logger.error(`Failed to cancel request ${requestId}`, error);
    }
  }

  /**
   * Cancel all active requests
   */
  cancelAllRequests() {
    try {
      const activeIds = Array.from(this.activeRequests.keys());
      activeIds.forEach(id => this.cancelRequest(id));
      this.logger.debug(`Cancelled ${activeIds.length} active requests`);
    } catch (error) {
      this.logger.error('Failed to cancel all requests', error);
    }
  }

  /**
   * Get active request count
   */
  getActiveRequestCount() {
    return this.activeRequests.size;
  }

  /**
   * Get active requests info
   */
  getActiveRequests() {
    return Array.from(this.activeRequests.values()).map(req => ({ ...req }));
  }

  /**
   * Private methods
   */
  generateRequestId() {
    this.requestCounter = (this.requestCounter + 1) % Number.MAX_SAFE_INTEGER;
    return this.requestCounter;
  }

  cleanupRequest(requestId) {
    this.activeRequests.delete(requestId);

    const abortController = this.abortControllers.get(requestId);
    if (abortController) {
      this.abortControllers.delete(requestId);
    }
  }

  cleanupOldestRequests() {
    const now = Date.now();
    const requestsToCleanup = [];

    for (const [id, tracking] of this.activeRequests) {
      if (now - tracking.startTime > this.options.requestTimeout) {
        requestsToCleanup.push(id);
      }
    }

    // If no timed-out requests, remove oldest ones
    if (requestsToCleanup.length === 0) {
      const sortedRequests = Array.from(this.activeRequests.entries())
        .sort(([, a], [, b]) => a.startTime - b.startTime);

      const excessCount = this.activeRequests.size - this.options.maxActiveRequests + 1;
      for (let i = 0; i < excessCount; i++) {
        requestsToCleanup.push(sortedRequests[i][0]);
      }
    }

    requestsToCleanup.forEach(id => {
      this.logger.debug(`Cleaning up request ${id}`);
      this.cleanupRequest(id);
    });
  }

  startCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanupOldestRequests();
    }, this.options.cleanupInterval);
  }

  /**
   * Cleanup all resources
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    this.cancelAllRequests();
    this.activeRequests.clear();
    this.abortControllers.clear();

    this.logger.debug('Progress tracker destroyed');
  }
}

/**
 * Store integration with dependency injection
 */
class StoreIntegration {
  constructor(store, options = {}) {
    this.store = store;
    this.options = {
      throttleDelay: 100,
      hideDelay: 500,
      ...options
    };

    this.logger = new Logger('StoreIntegration');
    this.updateThrottler = new Throttler(
      this.dispatchProgressUpdate.bind(this),
      this.options.throttleDelay
    );
  }

  loadProgressBar(trackingData) {
    try {
      this.store.dispatch('app/loadProgressBar', trackingData);
    } catch (error) {
      this.logger.error('Failed to load progress bar', error);
    }
  }

  updateProgress(trackingData) {
    if (trackingData) {
      this.updateThrottler.call(trackingData);
    }
  }

  dispatchProgressUpdate(trackingData) {
    try {
      this.store.dispatch('app/updateProgressData', trackingData);
    } catch (error) {
      this.logger.error('Failed to update progress data', error);
    }
  }

  unloadProgressBar(delay = null) {
    const hideDelay = delay !== null ? delay : this.options.hideDelay;

    setTimeout(() => {
      try {
        this.store.dispatch('app/unLoadProgressBar');
      } catch (error) {
        this.logger.error('Failed to unload progress bar', error);
      }
    }, hideDelay);
  }

  destroy() {
    this.updateThrottler.cancel();
  }
}

/**
 * Main axios integration service
 */
class AxiosProgressService {
  constructor(options = {}) {
    this.options = {
      tracker: {},
      store: { hideDelay: 500 },
      ...options
    };

    this.tracker = new ProgressTracker(this.options.tracker);
    this.storeIntegration = null;
    this.logger = new Logger('AxiosProgressService');
    this.isInitialized = false;
  }

  async initialize() {
    try {
      const store = await storePromise;
      this.storeIntegration = new StoreIntegration(store, this.options.store);
      this.setupInterceptors();
      this.isInitialized = true;
      this.logger.debug('Axios progress service initialized');
    } catch (error) {
      this.logger.error('Failed to initialize axios progress service', error);
      throw error;
    }
  }

  setupInterceptors() {
    // Request interceptor
    axios.interceptors.request.use(
      (config) => {
        try {
          const trackingData = this.tracker.startTracking(config);
          if (!trackingData) {
            this.logger.warn('Failed to start tracking for request');
            return config;
          }

          // Setup progress handlers
          this.setupProgressHandlers(config, trackingData);

          // Load progress bar
          this.storeIntegration.loadProgressBar(trackingData);

          return config;
        } catch (error) {
          this.logger.error('Request interceptor error', error);
          this.storeIntegration?.unloadProgressBar(0);
          return config;
        }
      },
      (error) => {
        this.storeIntegration?.unloadProgressBar(0);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    axios.interceptors.response.use(
      (response) => {
        try {
          if (response.config._progressTrackerId) {
            const finalTracking = this.tracker.finishTracking(
              response.config._progressTrackerId,
              'completed'
            );

            if (finalTracking) {
              this.storeIntegration.updateProgress({
                ...finalTracking,
                progress: 100,
                completed: true
              });
            }
          }

          this.storeIntegration.unloadProgressBar();
          return response;
        } catch (error) {
          this.logger.error('Response interceptor error', error);
          this.storeIntegration?.unloadProgressBar(0);
          return response;
        }
      },
      (error) => {
        try {
          if (error.config?._progressTrackerId) {
            const status = error.name === 'AbortError' ? 'cancelled' : 'failed';
            this.tracker.finishTracking(error.config._progressTrackerId, status);
          }

          this.storeIntegration?.unloadProgressBar(0);
          return Promise.reject(error);
        } catch (interceptorError) {
          this.logger.error('Error interceptor error', interceptorError);
          return Promise.reject(error);
        }
      }
    );
  }

  setupProgressHandlers(config, trackingData) {
    // Upload progress handler
    if (config.data instanceof FormData || config.method?.toLowerCase() === 'post') {
      config.onUploadProgress = (progressEvent) => {
        const updatedTracking = this.tracker.updateProgress(trackingData.id, progressEvent);
        if (updatedTracking) {
          this.storeIntegration.updateProgress(updatedTracking);
        }
      };
    }

    // Download progress handler
    config.onDownloadProgress = (progressEvent) => {
      const updatedTracking = this.tracker.updateProgress(trackingData.id, progressEvent);
      if (updatedTracking) {
        this.storeIntegration.updateProgress(updatedTracking);
      }
    };
  }

  // Public API methods
  getActiveRequestCount() {
    return this.tracker.getActiveRequestCount();
  }

  getActiveRequests() {
    return this.tracker.getActiveRequests();
  }

  cancelRequest(requestId) {
    this.tracker.cancelRequest(requestId);
  }

  cancelAllRequests() {
    this.tracker.cancelAllRequests();
  }

  destroy() {
    this.tracker.destroy();
    this.storeIntegration?.destroy();
    this.isInitialized = false;
    this.logger.debug('Axios progress service destroyed');
  }
}

// Create and initialize the service
const progressService = new AxiosProgressService();

// Initialize the service and expose global references
progressService.initialize().then(() => {
  // Expose utilities globally for manual usage
  window.axios = axios;
  window.progressService = progressService;
  window.ProgressFormatter = ProgressFormatter;
}).catch(error => {
  console.error('Failed to initialize progress service:', error);
});

export default progressService;
export { ProgressFormatter, ProgressTracker, StoreIntegration, AxiosProgressService };
